{"version": 3, "file": "code-analyzer.js", "sourceRoot": "", "sources": ["../../src/core/code-analyzer.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AA0CzC;;GAEG;AACH,MAAM,iBAAiB,GAAgD;IACrE,KAAK,EAAE;QACL,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;QAC1C,mBAAmB,EAAE;YACnB,sDAAsD;YACtD,wDAAwD;YACxD,uCAAuC;YACvC,mCAAmC;SACpC;QACD,cAAc,EAAE;YACd,0EAA0E;YAC1E,0CAA0C;SAC3C;QACD,cAAc,EAAE;YACd,yCAAyC;YACzC,mCAAmC;YACnC,mCAAmC;SACpC;KACF;IACD,GAAG,EAAE;QACH,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;QAClC,mBAAmB,EAAE;YACnB,mCAAmC;YACnC,yCAAyC;YACzC,sDAAsD;SACvD;QACD,cAAc,EAAE;YACd,+DAA+D;YAC/D,0CAA0C;YAC1C,sCAAsC;SACvC;QACD,cAAc,EAAE;YACd,oCAAoC;YACpC,mCAAmC;SACpC;KACF;IACD,MAAM,EAAE;QACN,UAAU,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC;QACrC,mBAAmB,EAAE;YACnB,mCAAmC;YACnC,mCAAmC;SACpC;QACD,cAAc,EAAE;YACd,+DAA+D;YAC/D,0CAA0C;SAC3C;QACD,cAAc,EAAE;YACd,uCAAuC;YACvC,mCAAmC;SACpC;KACF;IACD,OAAO,EAAE;QACP,UAAU,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;QACnC,mBAAmB,EAAE;YACnB,8CAA8C;YAC9C,kDAAkD;YAClD,6CAA6C;SAC9C;QACD,cAAc,EAAE;YACd,+DAA+D;YAC/D,0CAA0C;SAC3C;QACD,cAAc,EAAE;YACd,gDAAgD;YAChD,mCAAmC;SACpC;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,YAAY;IACf,UAAU,CAAuB;IAEzC,YAAY,UAAiC;QAC3C,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,QAAgB,EAChB,UAA+B,EAAE;QAEjC,MAAM,EACJ,gBAAgB,GAAG,IAAI,EACvB,SAAS,GAAG,IAAI,EAChB,gBAAgB,EAChB,eAAe,GAAG,CAAC,EACnB,eAAe,GAAG,EAAE,EACrB,GAAG,OAAO,CAAC;QAEZ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAElE,MAAM,MAAM,GAAuB;gBACjC,iBAAiB;gBACjB,gBAAgB,EAAE,EAAE;gBACpB,gBAAgB,EAAE,EAAE;gBACpB,WAAW,EAAE,EAAE;aAChB,CAAC;YAEF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CACpD,OAAO,EACP,iBAAiB,EACjB,eAAe,EACf,eAAe,CAChB,CAAC;YACJ,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CACjD,OAAO,EACP,iBAAiB,EACjB,gBAAgB,CACjB,CAAC;YACJ,CAAC;YAED,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAExE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACrH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAgB,EAAE,OAAe;QACvD,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC9B,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,mCAAmC;QACnC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpC,uCAAuC;gBACvC,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;oBACjE,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,IAAI,GAAG,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,GAAG,KAAK,SAAS,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACrE,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,6BAA6B;QAC7B,IAAI,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACpE,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAC7B,OAAe,EACf,SAAyC,EACzC,SAAiB,EACjB,eAAyB;QAEzB,MAAM,gBAAgB,GAAsB,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,kCAAkC;QAClC,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YACzE,mBAAmB;YACnB,yBAAyB;SAC1B,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;YAChC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzB,IAAI,KAAK,CAAC;gBACV,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC7C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBAEtB,qCAAqC;oBACrC,IAAI,CAAC,IAAI;wBAAE,SAAS;oBAEpB,oBAAoB;oBACpB,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS;wBAAE,SAAS;oBAEtC,mCAAmC;oBACnC,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAAE,SAAS;oBAElE,wDAAwD;oBACxD,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;wBAAE,SAAS;oBAExC,2CAA2C;oBAC3C,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE;wBAAE,SAAS;oBAE9D,gBAAgB,CAAC,IAAI,CAAC;wBACpB,IAAI;wBACJ,IAAI,EAAE,SAAS,GAAG,CAAC;wBACnB,MAAM,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC;wBACxB,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;wBAC1C,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;qBAChD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,oBAAoB,CAC1B,OAAe,EACf,SAAyC,EACzC,gBAAmC;QAEnC,MAAM,KAAK,GAAuB,EAAE,CAAC;QAErC,8CAA8C;QAC9C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAC9E,oFAAoF;YACpF,sDAAsD;YACtD,mCAAmC;SACpC,CAAC;QAEF,0EAA0E;QAC1E,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,KAAK,CAAC;YACV,kEAAkE;YAClE,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;YAEtB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAChD,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEzB,wCAAwC;gBACxC,IAAI,CAAC,OAAO;oBAAE,SAAS;gBAEvB,wEAAwE;gBACxE,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;oBAAE,SAAS;gBAEzC,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAExE,6CAA6C;gBAC7C,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;gBAClD,MAAM,gBAAgB,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACvD,MAAM,MAAM,GAAG,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,gBAAgB,GAAG,CAAC,CAAC;gBAE1F,KAAK,CAAC,IAAI,CAAC;oBACT,OAAO;oBACP,IAAI,EAAE,UAAU;oBAChB,MAAM;oBACN,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjB,MAAM;iBACP,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,OAAe;QAClC,kEAAkE;QAClE,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAE7C,6EAA6E;QAC7E,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAE/C,6DAA6D;QAC7D,IAAI,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAErD,8DAA8D;QAC9D,IAAI,0CAA0C,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAC1E,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC,CAAC,gBAAgB;QAE1D,4DAA4D;QAC5D,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEpC,yDAAyD;QACzD,IAAI,+BAA+B,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAE/D,iDAAiD;QACjD,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEvC,gDAAgD;QAChD,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAE3C,yDAAyD;QACzD,IAAI,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAE3D,kDAAkD;QAClD,IAAI,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEvD,6CAA6C;QAC7C,IAAI,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAE1D,4CAA4C;QAC5C,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEnC,gEAAgE;QAChE,4BAA4B;QAC5B,wBAAwB;QACxB,sBAAsB;QACtB,uBAAuB;QAEvB,oEAAoE;QACpE,oDAAoD;QAEpD,0CAA0C;QAC1C,IAAI,4CAA4C,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAC5E,IAAI,wEAAwE,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAExG,oDAAoD;QACpD,IAAI,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEhF,0CAA0C;QAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEvC,iCAAiC;QACjC,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,IAAI,CAAC;QAE1E,qDAAqD;QACrD,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAErC,+CAA+C;QAC/C,IAAI,kCAAkC,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAElE,oCAAoC;QACpC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEpC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,MAA0B,EAC1B,gBAAmC;QAEnC,MAAM,WAAW,GAAqB,EAAE,CAAC;QAEzC,uCAAuC;QACvC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC1C,IAAI,SAAS,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBAC/B,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,0CAA0C,SAAS,CAAC,IAAI,GAAG;oBACpE,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,MAAM,EAAE,iCAAiC,SAAS,CAAC,YAAY,EAAE;oBACjE,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClB,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,+BAA+B,KAAK,CAAC,OAAO,GAAG;oBACxD,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,4BAA4B,KAAK,CAAC,OAAO,EAAE;oBACnD,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAY;QACtC,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,aAAa;QAE9B,oDAAoD;QACpD,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,KAAK,IAAI,GAAG,CAAC,CAAC,sBAAsB;QAC7D,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,KAAK,IAAI,GAAG,CAAC,CAAC,wBAAwB;QAC/D,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,KAAK,IAAI,GAAG,CAAC,CAAC,iBAAiB;QAC/D,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE;YAAE,KAAK,IAAI,GAAG,CAAC,CAAC,cAAc;QAElD,6CAA6C;QAC7C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,KAAK,IAAI,GAAG,CAAC,CAAC,8BAA8B;QACnE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,KAAK,IAAI,GAAG,CAAC,CAAC,mBAAmB;QACtD,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;YAAE,KAAK,IAAI,GAAG,CAAC,CAAC,gBAAgB;QAE/D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,IAAY,EAAE,SAA8B;QAC9D,MAAM,OAAO,GAAG,IAAI;aACjB,WAAW,EAAE;aACb,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;aAC3B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;QAChD,OAAO,GAAG,MAAM,IAAI,OAAO,EAAE,CAAC;IAChC,CAAC;CACF"}