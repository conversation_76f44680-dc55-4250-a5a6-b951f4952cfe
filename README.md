# i18n MCP Server

A high-performance Model Context Protocol (MCP) server for managing internationalization (i18n) translation files. Built with TypeScript and designed for efficiency, this server provides intelligent translation management capabilities through MCP tools.

## 🚀 Features

### Core Capabilities
- **High-Performance Indexing**: O(1) translation lookups with in-memory indexing
- **Real-time File Watching**: Automatic detection and processing of translation file changes
- **Advanced Search**: Fast search across translation keys and values with scoring
- **Structure Validation**: Cross-language consistency checking and auto-fixing
- **Context Retrieval**: Hierarchical context with parent/child/sibling relationships
- **Batch Operations**: Atomic multi-operation transactions with rollback
- **Usage Analysis**: Dead code detection and duplicate value identification

### MCP Tools

#### Core Translation Tools
1. **`search_translation`** - Search for translations across keys and values
2. **`get_translation_context`** - Retrieve translations with hierarchical context
3. **`update_translation`** - Update translation values with validation
4. **`validate_structure`** - Check consistency across language files
5. **`check_translation_integrity`** - Comprehensive file integrity validation against base language
6. **`reorganize_translation_files`** - Reorganize translation files to match base language structure and key order
7. **`delete_translation_smart`** - Intelligently delete single or multiple translation keys with dependency checking
8. **`get_stats`** - Get server and index statistics

#### IDE Integration Tools
9. **`analyze_code_file`** - Analyze source code for hardcoded strings and translation usage
10. **`extract_to_translation`** - Extract hardcoded text and replace with translation calls
11. **`add_translation_smart`** - Intelligently add translations with conflict detection
12. **`get_translation_suggestions`** - Get autocomplete suggestions for translation keys
13. **`sync_translations_to_files`** - Sync translation index back to files
14. **`generate_types`** - Generate TypeScript types for translation keys
15. **`search_missing_translations`** - Find translation keys used in code but missing from translation files

## 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd i18n-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

## 🏃‍♂️ Quick Start

### 1. Prepare Translation Files

Create a `locales` directory with JSON translation files:

```
locales/
├── en.json    # Base language
├── es.json    # Spanish translations
├── fr.json    # French translations
└── ...
```

Example `en.json`:
```json
{
  "common": {
    "buttons": {
      "submit": "Submit",
      "cancel": "Cancel"
    }
  },
  "auth": {
    "login": {
      "title": "Sign In"
    }
  }
}
```

### 2. Start the MCP Server

```bash
# Start with default settings (watches ./locales)
npm start

# Start with custom directory and debug mode
node dist/index.js --dir ./i18n --debug

# Show help
node dist/index.js --help
```

### 3. Use with MCP Clients

The server communicates via STDIO transport and can be used with any MCP-compatible client like Claude Desktop.

## 🔧 Configuration

### Command Line Options

```bash
Usage: i18n-mcp [options] [translation-directory]

Basic Options:
  -d, --dir <path>           Translation files directory (default: ./locales)
  -b, --base-language <lang> Base language for structure template (default: en)
  -n, --name <name>          Server name (default: i18n-mcp)
  -v, --version <version>    Server version (default: 1.0.0)
  --debug                    Enable debug logging
  -h, --help                 Show help message

IDE Integration Options:
  --src-dir <path>           Source code directory to analyze
  --exclude <patterns>       Exclude patterns for code analysis (comma-separated)
  --auto-sync                Auto-write changes back to files
  --generate-types <path>    Generate TypeScript types file
  --watch-code               Watch source files for changes
  --project-root <path>      Project root for relative paths
  --frameworks <list>        Framework-specific analysis (react,vue,svelte,angular)
  --key-style <style>        Key naming style (nested|flat|camelCase|kebab-case)
```

### Environment Variables

```bash
# Basic Configuration
I18N_MCP_DIR              # Translation directory
I18N_MCP_BASE_LANGUAGE    # Base language
I18N_MCP_DEBUG            # Enable debug mode (true/false)

# IDE Integration
I18N_MCP_SRC_DIR          # Source code directory
I18N_MCP_AUTO_SYNC        # Auto-sync mode (true/false)
I18N_MCP_EXCLUDE          # Exclude patterns (comma-separated)
I18N_MCP_GENERATE_TYPES   # TypeScript types file path
I18N_MCP_WATCH_CODE       # Watch source files (true/false)
I18N_MCP_PROJECT_ROOT     # Project root directory
I18N_MCP_FRAMEWORKS       # Supported frameworks (comma-separated)
I18N_MCP_KEY_STYLE        # Key naming style
```

## 🛠️ MCP Tools Usage

### Search Translations

```json
{
  "tool": "search_translation",
  "arguments": {
    "query": "submit",
    "scope": "both",
    "languages": ["en", "es"],
    "maxResults": 10,
    "caseSensitive": false
  }
}
```

### Get Translation Context

```json
{
  "tool": "get_translation_context",
  "arguments": {
    "keyPath": "common.buttons.submit",
    "contextDepth": 1,
    "languages": "all"
  }
}
```

### Update Translation

```json
{
  "tool": "update_translation",
  "arguments": {
    "keyPath": "common.buttons.submit",
    "updates": {
      "en": "Submit Form",
      "es": "Enviar Formulario"
    },
    "validateStructure": true
  }
}
```

### Validate Structure

```json
{
  "tool": "validate_structure",
  "arguments": {
    "baseLanguage": "en",
    "fix": false
  }
}
```

### Analyze Code File

```json
{
  "tool": "analyze_code_file",
  "arguments": {
    "filePath": "src/components/Button.tsx",
    "extractHardcoded": true,
    "findUsage": true,
    "frameworks": ["react"],
    "minStringLength": 3,
    "excludePatterns": ["https?://.*"]
  }
}
```

### Extract to Translation

```json
{
  "tool": "extract_to_translation",
  "arguments": {
    "filePath": "src/components/Button.tsx",
    "textToExtract": "Click me",
    "targetKey": "common.buttons.click_me",
    "replaceInFile": true,
    "framework": "react",
    "additionalLanguages": {
      "es": "Haz clic",
      "fr": "Cliquez-moi"
    }
  }
}
```

### Generate TypeScript Types

```json
{
  "tool": "generate_types",
  "arguments": {
    "outputPath": "./src/types/i18n.ts",
    "namespace": "I18n",
    "includeValues": false,
    "strict": true,
    "watch": true
  }
}
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:core
npm run test:integration

# Run simple functionality test
npx tsx simple-test.ts

# Run development server test
npx tsx test-server.ts
```

## 🎯 IDE Integration Demo

Try the complete IDE integration workflow:

```bash
# Run the interactive demo
npx tsx examples/ide-integration-demo.ts
```

This demo will:
1. Create sample React components with hardcoded strings
2. Analyze the code for translation opportunities
3. Extract hardcoded strings to translation files
4. Generate TypeScript types for translation keys
5. Show autocomplete suggestions and validation

### Example IDE Workflow

```bash
# Start server with IDE features
node dist/index.js \
  --dir ./locales \
  --src-dir ./src \
  --auto-sync \
  --generate-types ./src/types/i18n.ts \
  --frameworks "react,vue" \
  --key-style "nested"
```

## 📊 Performance Features

- **LRU Caching**: Configurable cache for frequently accessed translations
- **Binary Search**: Optimized prefix searches on sorted key indices
- **Debounced File Operations**: Prevents thrashing during rapid file changes
- **Memory Efficient**: Minimal memory footprint with smart indexing

## 🏗️ Architecture

### Core Components

#### Translation Management
1. **TranslationIndex**: High-performance in-memory index with O(1) lookups
2. **FileWatcher**: Chokidar-based file monitoring with debounced processing
3. **MCPServer**: Main server implementation with tool registration
4. **JsonOperations**: Safe JSON parsing and manipulation utilities
5. **PathParser**: Efficient dot-notation path parsing with caching

#### IDE Integration
6. **CodeAnalyzer**: Framework-aware code analysis for hardcoded strings
7. **TranslationExtractor**: Smart extraction and replacement of hardcoded text
8. **TypeGenerator**: TypeScript type generation for translation keys
9. **Advanced MCP Tools**: 11 specialized tools for IDE integration

### Data Flow

#### Basic Translation Management
```
Translation Files → File Watcher → Translation Index → MCP Tools → Client
```

#### IDE Integration Workflow
```
Source Code → Code Analyzer → Hardcoded Strings Detection
     ↓
Translation Extractor → Smart Key Generation → Translation Index
     ↓
Type Generator → TypeScript Types → IDE Autocomplete
     ↓
MCP Tools → Real-time Sync → Translation Files
```

## 🔍 Example Output

### Translation File Reorganization
```json
{
  "success": true,
  "baseLanguage": "en",
  "totalFiles": 3,
  "summary": {
    "filesReorganized": 2,
    "keysAdded": 5,
    "keysRemoved": 3,
    "filesSkipped": 0,
    "errors": 0
  },
  "fileResults": {
    "fr": {
      "language": "fr",
      "filePath": "/path/to/fr.json",
      "reorganized": true,
      "changes": {
        "keysAdded": 3,
        "keysRemoved": 1,
        "keysReordered": true
      }
    },
    "de": {
      "language": "de",
      "filePath": "/path/to/de.json",
      "reorganized": true,
      "changes": {
        "keysAdded": 2,
        "keysRemoved": 2,
        "keysReordered": true
      }
    }
  },
  "recommendations": [
    "✅ Successfully reorganized 2 files",
    "📝 5 missing keys were added with base language values as placeholders",
    "🗑️ 3 extra keys were removed",
    "🔄 Consider running the check_translation_integrity tool to verify the reorganization"
  ]
}
```

### Search Results
```json
{
  "query": "submit",
  "resultsCount": 1,
  "results": [
    {
      "keyPath": "common.buttons.submit",
      "matchType": "both",
      "score": 1,
      "translations": {
        "en": { "value": "Submit", "file": "locales/en.json" },
        "es": { "value": "Enviar", "file": "locales/es.json" }
      }
    }
  ]
}
```

### Structure Validation
```json
{
  "valid": false,
  "summary": {
    "missingKeysCount": 3,
    "extraKeysCount": 0,
    "typeMismatchesCount": 0
  },
  "details": {
    "missingKeys": {
      "fr": ["common.buttons.edit", "auth.register.title"]
    }
  }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- Built with the [Model Context Protocol SDK](https://github.com/modelcontextprotocol/typescript-sdk)
- File watching powered by [Chokidar](https://github.com/paulmillr/chokidar)
- Type validation with [Zod](https://github.com/colinhacks/zod)
